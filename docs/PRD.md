# BonKai: Detailed Technical Product Requirements Document

## Executive Summary

<PERSON><PERSON><PERSON> is an innovative Web3 meme project that combines NFTs, AI agents, and gamification on the Solana blockchain. This PRD provides comprehensive technical specifications and implementation patterns for building a multi-platform ecosystem featuring a 6969-supply NFT collection, multi-tier staking system, AI-powered agents with token-gated access, and cross-platform integration via web and Telegram interfaces.

## 1. System Architecture Overview

### Technology Stack
- **Frontend**: Next.js 15 (App Router with experimental PPR)
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: NextAuth.js v5 beta with Web3 wallet integration
- **Package Manager**: pnpm
- **AI Framework**: Vercel AI SDK v5 beta with xAI Grok models
- **Models**: Grok-2-Vision-1212, Grok-3-Mini-Beta, Grok-2-1212, Grok-2-Image
- **File Storage**: Vercel Blob
- **Blockchain**: Solana
- **Styling**: Tailwind CSS with shadcn/ui components
- **Code Quality**: Biome for linting/formatting
- **Testing**: Playwright for E2E testing

### Project Structure (Current Codebase)
```
bonkai/
├── app/
│   ├── (auth)/              # Authentication routes and API
│   ├── (chat)/              # Main chat interface and API routes
│   ├── layout.tsx           # Root layout with providers
│   └── globals.css          # Global styles
├── artifacts/               # Document generation system
│   ├── code/                # Code artifact handling
│   ├── image/               # Image artifact handling
│   ├── sheet/               # Spreadsheet artifact handling
│   └── text/                # Text artifact handling
├── components/              # Reusable UI components
│   ├── ui/                  # shadcn/ui components
│   └── [chat-components]    # Chat-specific components
├── lib/
│   ├── ai/                  # AI providers and models
│   ├── db/                  # Database schema and migrations
│   └── utils.ts             # Utility functions
├── hooks/                   # Custom React hooks
├── tests/                   # Playwright test suites
└── docs/                    # Documentation
```

## 2. Blockchain Implementation

### 2.1 NFT Collection (6969 Supply) -- WE DON'T CODE THOSE 

**5-Tier Structure Implementation**
  { name: 'Common', supply: 3500, power: 10, stakingTier: 0 },
  { name: 'Uncommon', supply: 2000, power: 25, stakingTier: 0 },
  { name: 'Rare', supply: 1000, power: 50, stakingTier: 1 },
  { name: 'Epic', supply: 400, power: 100, stakingTier: 1 },
  { name: 'Legendary', supply: 69, power: 250, stakingTier: 2 }

**Minting with BonKai Token**
```typescript
const purchaseNFTWithToken = async (
  buyer: PublicKey,
  price: number,
  bonkaiTokenMint: PublicKey
) => {
  const buyerTokenAccount = await createAssociatedTokenAccount(
    connection,
    buyer,
    bonkaiTokenMint,
    buyer
  );
  
  await transfer(
    connection,
    buyer,
    buyerTokenAccount,
    treasuryTokenAccount,
    buyer,
    price * Math.pow(10, 9)
  );
};
```

### 2.2 Multi-Tier Staking System

**Staking Smart Contract (Anchor)**
```rust
#[program]
pub mod bonkai_staking {
    pub fn stake_nft(
        ctx: Context<StakeNFT>,
        nft_tier: u8, // 0=Common/Uncommon, 1=Rare/Epic, 2=Legendary
    ) -> Result<()> {
        let staking_pool = &mut ctx.accounts.staking_pool;
        let user_stake = &mut ctx.accounts.user_stake;
        let clock = Clock::get()?;
        
        // Transfer NFT to staking vault
        // Initialize stake record with tier-based rewards
        user_stake.staking_tier = nft_tier;
        user_stake.last_claim_time = clock.unix_timestamp;
        
        Ok(())
    }
}
```

**Tier-Based Access Levels**
- Bronze Tier ($20): Basic AI access, 100K tokens/month
- Silver Tier ($50): Premium AI access, 500K tokens/month
- Diamond Tier ($100): Full AI access, 2M tokens/month

## 3. Authentication System

### 3.1 NextAuth.js v5 + Web3 Wallet Integration

**Enhanced Authentication Configuration**
```typescript
// app/(auth)/auth.ts
import NextAuth from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import { compare } from 'bcrypt-ts';
import { getUser, createGuestUser } from '@/lib/db/queries';

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  pages: {
    signIn: '/login',
    newUser: '/',
  },
  providers: [
    Credentials({
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
        walletAddress: { label: 'Wallet Address', type: 'text' }
      },
      async authorize({ email, password, walletAddress }: any) {
        const users = await getUser(email);

        if (users.length === 0) {
          return null;
        }

        const [user] = users;

        // Verify wallet signature if provided
        if (walletAddress) {
          const isValidWallet = await verifyWalletSignature(walletAddress, user.id);
          if (!isValidWallet) return null;
        }

        if (!user.password) return null;

        const passwordsMatch = await compare(password, user.password);
        if (!passwordsMatch) return null;

        return {
          ...user,
          type: 'regular',
          walletAddress: walletAddress || user.walletAddress
        };
      },
    }),
    Credentials({
      id: 'guest',
      credentials: {},
      async authorize() {
        const [guestUser] = await createGuestUser();
        return { ...guestUser, type: 'guest' };
      },
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      if (token.sub) {
        session.user.id = token.sub;
        session.user.walletAddress = token.walletAddress as string;
        session.user.tier = token.tier as string;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.walletAddress = user.walletAddress;
        token.tier = user.tier;
      }
      return token;
    },
  },
});
```

### 3.2 Token-Gating Middleware

```typescript
// middleware.ts
import { auth } from '@/app/(auth)/auth';
import { NextRequest, NextResponse } from 'next/server';

export async function tokenGateMiddleware(
  req: NextRequest,
  requiredTier: string
): Promise<NextResponse | null> {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  const userWallet = session.user.walletAddress;
  const hasAccess = await checkTokenOwnership(userWallet, requiredTier);

  if (!hasAccess) {
    return NextResponse.json(
      { error: 'Insufficient token balance for access' },
      { status: 403 }
    );
  }

  return null;
}

export default auth((req) => {
  // Apply token gating to specific routes
  if (req.nextUrl.pathname.startsWith('/api/ai/premium')) {
    return tokenGateMiddleware(req, 'SILVER');
  }

  if (req.nextUrl.pathname.startsWith('/api/ai/diamond')) {
    return tokenGateMiddleware(req, 'DIAMOND');
  }
});
```

## 4. AI Agent Implementation

### 4.1 Vercel AI SDK v5 Multi-Model Configuration

```typescript
// lib/ai/providers.ts
import { customProvider, extractReasoningMiddleware, wrapLanguageModel } from 'ai';
import { xai } from '@ai-sdk/xai';

export const bonkaiProvider = customProvider({
  languageModels: {
    'chat-model': xai('grok-2-vision-1212'),
    'chat-model-reasoning': wrapLanguageModel({
      model: xai('grok-3-mini-beta'),
      middleware: extractReasoningMiddleware({ tagName: 'think' }),
    }),
    'title-model': xai('grok-2-1212'),
    'artifact-model': xai('grok-2-1212'),
  },
  imageModels: {
    'image-model': xai.imageModel('grok-2-image'),
  },
});

// Tier-based model access
export const models = {
  bronze: {
    chat: 'chat-model',
    reasoning: null, // No reasoning for bronze
    image: null, // No image generation for bronze
  },
  silver: {
    chat: 'chat-model',
    reasoning: 'chat-model-reasoning',
    image: 'image-model',
  },
  diamond: {
    chat: 'chat-model',
    reasoning: 'chat-model-reasoning',
    image: 'image-model',
    artifact: 'artifact-model',
  }
};
```

### 4.2 Task Complexity Routing with Current Models

```typescript
// lib/ai/models.ts
export interface ChatModel {
  id: string;
  name: string;
  description: string;
  tier: 'bronze' | 'silver' | 'diamond';
}

export const chatModels: Array<ChatModel> = [
  {
    id: 'chat-model',
    name: 'Grok-2 Vision',
    description: 'Primary model for all-purpose chat with vision capabilities',
    tier: 'bronze',
  },
  {
    id: 'chat-model-reasoning',
    name: 'Grok-3 Mini Reasoning',
    description: 'Advanced reasoning model with step-by-step thinking',
    tier: 'silver',
  },
];

export function getModelForTask(
  complexity: TaskComplexity,
  userTier: UserTier,
  taskType: 'text' | 'image' | 'reasoning' = 'text'
) {
  // Enforce tier restrictions
  if (userTier === 'BRONZE' && taskType === 'reasoning') {
    return 'chat-model'; // Downgrade to basic model
  }

  if (userTier === 'BRONZE' && taskType === 'image') {
    throw new Error('Image generation requires Silver tier or higher');
  }

  switch (taskType) {
    case 'reasoning':
      return userTier === 'BRONZE' ? 'chat-model' : 'chat-model-reasoning';
    case 'image':
      return userTier !== 'BRONZE' ? 'image-model' : null;
    case 'text':
    default:
      return 'chat-model';
  }
}
```

### 4.3 Enhanced Chat API Integration

```typescript
// app/(chat)/api/chat/route.ts
import { bonkaiProvider } from '@/lib/ai/providers';
import { auth } from '@/app/(auth)/auth';
import { streamText, convertToCoreMessages } from 'ai';

export async function POST(request: Request) {
  const { messages, selectedChatModel } = await request.json();
  const session = await auth();

  if (!session?.user?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check user tier and model access
  const userTier = session.user.tier || 'BRONZE';
  const allowedModel = getModelForTask('SIMPLE', userTier,
    selectedChatModel.includes('reasoning') ? 'reasoning' : 'text'
  );

  if (!allowedModel) {
    return new Response('Model not available for your tier', { status: 403 });
  }

  const result = await streamText({
    model: bonkaiProvider(allowedModel),
    messages: convertToCoreMessages(messages),
    system: systemPrompt({ selectedChatModel, userTier }),
    maxTokens: getTierTokenLimit(userTier),
    onFinish: async ({ text, usage }) => {
      // Log usage for billing/rate limiting
      await logAIUsage(session.user.id, usage, userTier);
    },
  });

  return result.toDataStreamResponse();
}
```

## 5. Telegram Bot Integration

### 5.1 grammY Bot Setup

```typescript
import { Bot, Context, SessionFlavor } from "grammy";

interface SessionData {
  walletAddress?: string;
  tier?: string;
  authenticated?: boolean;
}

type BotContext = Context & SessionFlavor<SessionData>;
const bot = new Bot<BotContext>(process.env.BOT_TOKEN);
```

### 5.2 Bankai Moves Gamification

```typescript
const bankaiMoves = {
  "fire_slash": { damage: 100, cost: 0.05 },
  "water_shield": { defense: 50, cost: 0.05 },
  "earth_stomp": { damage: 150, cost: 0.05 },
  "wind_dash": { speed: 200, cost: 0.05 }
};

bot.command("bankai", async (ctx) => {
  const keyboard = new InlineKeyboard();
  
  Object.entries(bankaiMoves).forEach(([move, stats]) => {
    keyboard.text(`${move} (${stats.cost} SOL)`, `bankai_${move}`);
    keyboard.row();
  });
  
  await ctx.reply("Choose your Bankai move:", { reply_markup: keyboard });
});
```

### 5.3 WebSocket Real-time Sync

```typescript
const server = serve({
  port: 3001,
  websocket: {
    message(ws, message) {
      const data = JSON.parse(message);
      
      switch (data.type) {
        case "auth":
          handleAuth(ws, data);
          break;
        case "transaction":
          broadcastTransaction(data);
          break;
      }
    }
  }
});
```

## 6. Database Schema

### 6.1 Core Tables (Drizzle ORM)

```typescript
// Convex schema example (see convex/schema.ts for full implementation)
export default defineSchema({
  users: defineTable({
    clerkId: v.string(),
    email: v.string(),
    walletAddress: v.optional(v.string()),
    tier: v.union(
      v.literal("FREE"),
      v.literal("BRONZE"),
      v.literal("SILVER"),
      v.literal("DIAMOND")
    ),
    tokenBalance: v.number(),
    createdAt: v.string(),
  }).index("by_clerk_id", ["clerkId"])
    .index("by_wallet", ["walletAddress"]),

  bankaiMoves: defineTable({
    userId: v.id("users"),
    moveType: v.string(),
    damage: v.number(),
    cost: v.string(),
    timestamp: v.string(),
    txHash: v.optional(v.string()),
  }).index("by_user", ["userId"])
    .index("by_timestamp", ["timestamp"]),
});
```

### 6.2 Performance Optimization

Convex provides built-in performance optimizations:
- Automatic indexing and query optimization
- Real-time subscriptions with efficient updates
- Built-in caching and connection pooling
- Serverless scaling

## 7. Frontend Implementation

### 7.1 Component Architecture

**Reusable Web3 Components**
```typescript
// Wallet Connect Component
export default function WalletConnectButton({
  onConnect,
  onDisconnect
}: WalletConnectButtonProps) {
  const { connect, connectors, isPending } = useConnect();
  const { isConnected, address } = useAccount();
  
  if (isConnected) {
    return (
      <div className="flex items-center space-x-2">
        <span>{address?.slice(0, 6)}...{address?.slice(-4)}</span>
        <button onClick={onDisconnect}>Disconnect</button>
      </div>
    );
  }
  
  return connectors.map((connector) => (
    <button
      key={connector.id}
      onClick={() => connect({ connector })}
      disabled={isPending}
    >
      Connect {connector.name}
    </button>
  ));
}
```

### 7.2 State Management

```typescript
export const useWalletStore = create<WalletState>()(
  persist(
    (set) => ({
      isConnected: false,
      address: null,
      balance: 0,
      transactions: [],
      setConnected: (connected) => set({ isConnected: connected }),
      addTransaction: (transaction) =>
        set((state) => ({
          transactions: [...state.transactions, transaction]
        }))
    }),
    { name: 'wallet-storage' }
  )
);
```

## 8. File Storage Integration

### 8.1 Uploadthing Configuration

```typescript
export const uploadRouter = {
  aiImageUpload: f(['image'])
    .middleware(async ({ req }) => {
      const user = await auth(req);
      const userTier = await getUserTier(user.id);
      const quota = await checkUploadQuota(user.id, userTier);
      
      if (!quota.canUpload) {
        throw new Error('Upload quota exceeded');
      }
      
      return { userId: user.id, userTier };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      await saveAIAsset({
        userId: metadata.userId,
        fileUrl: file.url,
        assetType: 'image',
        generationPrompt: metadata.prompt,
      });
    }),
};
```

## 9. Security Implementation

### 9.1 Rate Limiting

```typescript
const rateLimiters = {
  [UserTier.BRONZE]: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(50, '1 h'),
  }),
  [UserTier.SILVER]: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(200, '1 h'),
  }),
  [UserTier.DIAMOND]: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(1000, '1 h'),
  })
};
```

### 9.2 Message Signing Verification

```typescript
export function verifySignature(
  message: SignMessage,
  signature: string,
  walletAddress: string
): boolean {
  const messageBytes = new TextEncoder().encode(formatSignMessage(message));
  const signatureBytes = bs58.decode(signature);
  const publicKeyBytes = new PublicKey(walletAddress).toBytes();
  
  return nacl.sign.detached.verify(messageBytes, signatureBytes, publicKeyBytes);
}
```

## 10. Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
- Deploy BonKai SPL token
- Set up Turborepo structure
- Implement basic authentication with Clerk
- Create database schemas

### Phase 2: NFT System (Weeks 3-4)
- Deploy Metaplex Core collection
- Implement minting functionality
- Create staking contracts
- Build NFT display components

### Phase 3: AI Integration (Weeks 5-6)
- Integrate AISDK with OpenRouter
- Implement token-gated access
- Add MCP connections
- Deploy file storage system

### Phase 4: Platform Integration (Weeks 7-8)
- Deploy Telegram bot
- Implement WebSocket sync
- Add gamification features
- Complete cross-platform testing

### Phase 5: Launch Preparation (Weeks 9-10)
- Security audit
- Performance optimization
- Documentation completion
- Mainnet deployment

## 11. Cost Estimates

- **NFT Minting**: ~0.0037 SOL per mint (Metaplex Core)
- **Staking Operations**: ~0.005 SOL per transaction
- **AI Usage**: Variable based on model selection
- **Infrastructure**: ~$500-1000/month (Neon, Vercel, Uploadthing)

## 12. Key Technical Advantages

1. **Cost Efficiency**: 70% reduction using Metaplex Core
2. **Scalability**: Turborepo enables efficient development
3. **Security**: Multi-layer authentication and rate limiting
4. **Performance**: Optimistic UI patterns and caching
5. **Cross-Platform**: Unified experience across web and Telegram

This PRD provides a comprehensive technical foundation for building the BonKai Web3 ecosystem, combining cutting-edge blockchain technology with AI capabilities and engaging gamification features.